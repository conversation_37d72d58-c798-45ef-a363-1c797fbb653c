import React, { useState, useRef, useEffect } from 'react';
import * as THREE from 'three';
import { useGameState } from './hooks/useGameState';
import { useControls } from './hooks/useControls';
import { CameraMode } from './types/Game';

// Simple 3D Scene Component
const Simple3DScene: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    // Create scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color('#87CEEB');

    // Create camera
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 5, 10);

    // Create renderer
    const renderer = new THREE.WebGLRenderer({ canvas: canvasRef.current, antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // Add lighting
    const ambientLight = new THREE.AmbientLight('#ffffff', 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight('#ffffff', 1);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // Create ground
    const groundGeometry = new THREE.PlaneGeometry(100, 100);
    const groundMaterial = new THREE.MeshLambertMaterial({ color: '#059669' });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    scene.add(ground);

    // Create a simple car
    const carGroup = new THREE.Group();

    // Car body
    const bodyGeometry = new THREE.BoxGeometry(2, 1, 4);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: '#3B82F6' });
    const carBody = new THREE.Mesh(bodyGeometry, bodyMaterial);
    carBody.position.y = 0.5;
    carBody.castShadow = true;
    carGroup.add(carBody);

    // Car wheels
    const wheelGeometry = new THREE.CylinderGeometry(0.3, 0.3, 0.2, 8);
    const wheelMaterial = new THREE.MeshLambertMaterial({ color: '#1F2937' });

    const wheelPositions = [
      { x: -1, z: 1.5 }, { x: 1, z: 1.5 },
      { x: -1, z: -1.5 }, { x: 1, z: -1.5 }
    ];

    wheelPositions.forEach(pos => {
      const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
      wheel.position.set(pos.x, 0.3, pos.z);
      wheel.rotation.z = Math.PI / 2;
      wheel.castShadow = true;
      carGroup.add(wheel);
    });

    carGroup.position.y = 0.5;
    scene.add(carGroup);

    // Set fixed camera position
    camera.position.set(0, 8, 15);
    camera.lookAt(carGroup.position);

    // Animation loop
    let animationId: number;
    const animate = () => {
      animationId = requestAnimationFrame(animate);

      // Just render the scene (no auto-rotation)
      renderer.render(scene, camera);
    };

    animate();

    // Handle resize
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      cancelAnimationFrame(animationId);
      renderer.dispose();
    };
  }, []);

  return <canvas ref={canvasRef} className="fixed inset-0 w-full h-full pointer-events-none" />;
};

function App() {
  const { gameState, startGame, pauseGame, resetGame, updateGameState } = useGameState();
  const controls = useControls();
  const [cameraMode, setCameraMode] = useState<CameraMode>('chase');

  const handleCameraChange = (mode: CameraMode) => {
    setCameraMode(mode);
  };

  return (
    <div className="relative w-full h-screen overflow-hidden bg-gradient-to-b from-blue-900 to-blue-600">
      {/* Simple start screen without complex components */}
      {!gameState.isPlaying ? (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-black/40 backdrop-blur-md rounded-2xl p-8 border border-white/20 text-center max-w-md mx-4">
            <h1 className="text-4xl font-bold text-white mb-2">
              3D Racing Game
            </h1>
            <p className="text-blue-300 mb-6">
              Experience high-speed racing in stunning 3D
            </p>

            <button
              onClick={startGame}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700
                       text-white font-bold py-4 px-8 rounded-xl transition-all duration-200
                       transform hover:scale-105 flex items-center space-x-2 mx-auto"
            >
              <span>START RACE</span>
            </button>

            <div className="mt-6 text-sm text-gray-300">
              <p><strong>Controls:</strong></p>
              <p>WASD / Arrow Keys - Drive</p>
              <p>Space - Brake</p>
              <p>Shift - Boost</p>
            </div>
          </div>
        </div>
      ) : (
        <>
          {/* 3D Scene */}
          <Simple3DScene />

          {/* Game HUD */}
          <div className="absolute top-4 left-4 bg-black/20 backdrop-blur-md rounded-xl p-4 border border-white/10 text-white pointer-events-auto">
            <div className="text-lg font-bold">Speed: {Math.round(gameState.speed)} km/h</div>
            <div className="text-sm">Use WASD to control the car</div>
          </div>

          {/* Reset button */}
          <div className="absolute top-4 right-4">
            <button
              onClick={resetGame}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-xl transition-all pointer-events-auto"
            >
              Reset Game
            </button>
          </div>
        </>
      )}
    </div>
  );
}

export default App;