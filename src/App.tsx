import React, { useState } from 'react';
import { GameCanvas } from './components/GameCanvas';
import { GameHUD } from './components/GameHUD';
import { useGameState } from './hooks/useGameState';
import { useControls } from './hooks/useControls';
import { CameraMode } from './types/Game';

function App() {
  const { gameState, startGame, pauseGame, resetGame, updateGameState } = useGameState();
  const controls = useControls();
  const [cameraMode, setCameraMode] = useState<CameraMode>('chase');

  const handleCameraChange = (mode: CameraMode) => {
    setCameraMode(mode);
  };

  return (
    <div className="relative w-full h-screen overflow-hidden bg-gradient-to-b from-blue-900 to-blue-600">
      <GameCanvas
        controls={controls}
        gameState={gameState}
        onGameStateUpdate={updateGameState}
        cameraMode={cameraMode}
      />
      
      <GameHUD
        gameState={gameState}
        onStartGame={startGame}
        onPauseGame={pauseGame}
        onResetGame={resetGame}
        onCameraChange={handleCameraChange}
        currentCamera={cameraMode}
      />
    </div>
  );
}

export default App;