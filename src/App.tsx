import React, { useState } from 'react';
import { useGameState } from './hooks/useGameState';
import { useControls } from './hooks/useControls';
import { CameraMode } from './types/Game';

function App() {
  const { gameState, startGame, pauseGame, resetGame, updateGameState } = useGameState();
  const controls = useControls();
  const [cameraMode, setCameraMode] = useState<CameraMode>('chase');

  const handleCameraChange = (mode: CameraMode) => {
    setCameraMode(mode);
  };

  return (
    <div className="relative w-full h-screen overflow-hidden bg-gradient-to-b from-blue-900 to-blue-600">
      {/* Simple start screen without complex components */}
      {!gameState.isPlaying ? (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-black/40 backdrop-blur-md rounded-2xl p-8 border border-white/20 text-center max-w-md mx-4">
            <h1 className="text-4xl font-bold text-white mb-2">
              3D Racing Game
            </h1>
            <p className="text-blue-300 mb-6">
              Experience high-speed racing in stunning 3D
            </p>

            <button
              onClick={startGame}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700
                       text-white font-bold py-4 px-8 rounded-xl transition-all duration-200
                       transform hover:scale-105 flex items-center space-x-2 mx-auto"
            >
              <span>START RACE</span>
            </button>

            <div className="mt-6 text-sm text-gray-300">
              <p><strong>Controls:</strong></p>
              <p>WASD / Arrow Keys - Drive</p>
              <p>Space - Brake</p>
              <p>Shift - Boost</p>
            </div>
          </div>
        </div>
      ) : (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-white text-2xl">
            Game Started! Speed: {Math.round(gameState.speed)} km/h
            <br />
            <button
              onClick={resetGame}
              className="mt-4 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
            >
              Reset Game
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;