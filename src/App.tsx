import React, { useState, useRef, useEffect } from 'react';
import * as THREE from 'three';
import { useGameState } from './hooks/useGameState';
import { useControls } from './hooks/useControls';
import { CameraMode } from './types/Game';

// Simple 3D Scene Component
interface Simple3DSceneProps {
  onSpeedUpdate: (speed: number) => void;
}

const Simple3DScene: React.FC<Simple3DSceneProps> = ({ onSpeedUpdate }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const controls = useControls();

  useEffect(() => {
    if (!canvasRef.current) return;

    // Create scene
    const scene = new THREE.Scene();
    scene.background = new THREE.Color('#87CEEB');

    // Create camera
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.set(0, 5, 10);

    // Create renderer
    const renderer = new THREE.WebGLRenderer({ canvas: canvasRef.current, antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // Add lighting
    const ambientLight = new THREE.AmbientLight('#ffffff', 0.6);
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight('#ffffff', 1);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // Create textured ground
    const groundGeometry = new THREE.PlaneGeometry(200, 200, 50, 50);
    const groundMaterial = new THREE.MeshLambertMaterial({
      color: '#2D5016',
      wireframe: false
    });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    scene.add(ground);

    // Create a race track
    const trackGeometry = new THREE.RingGeometry(15, 25, 32);
    const trackMaterial = new THREE.MeshLambertMaterial({ color: '#374151' });
    const track = new THREE.Mesh(trackGeometry, trackMaterial);
    track.rotation.x = -Math.PI / 2;
    track.position.y = 0.01;
    track.receiveShadow = true;
    scene.add(track);

    // Add track center decoration
    const centerGeometry = new THREE.CircleGeometry(15, 32);
    const centerMaterial = new THREE.MeshLambertMaterial({ color: '#059669' });
    const center = new THREE.Mesh(centerGeometry, centerMaterial);
    center.rotation.x = -Math.PI / 2;
    center.position.y = 0.005;
    center.receiveShadow = true;
    scene.add(center);

    // Create a detailed car
    const carGroup = new THREE.Group();

    // Car body (main)
    const bodyGeometry = new THREE.BoxGeometry(1.8, 0.8, 4);
    const bodyMaterial = new THREE.MeshLambertMaterial({
      color: '#FF1744',
      transparent: true,
      opacity: 0.9
    });
    const carBody = new THREE.Mesh(bodyGeometry, bodyMaterial);
    carBody.position.y = 0.4;
    carBody.castShadow = true;
    carGroup.add(carBody);

    // Car roof
    const roofGeometry = new THREE.BoxGeometry(1.4, 0.6, 2);
    const roofMaterial = new THREE.MeshLambertMaterial({ color: '#C62828' });
    const carRoof = new THREE.Mesh(roofGeometry, roofMaterial);
    carRoof.position.set(0, 1, -0.3);
    carRoof.castShadow = true;
    carGroup.add(carRoof);

    // Car wheels with rims
    const wheelGeometry = new THREE.CylinderGeometry(0.4, 0.4, 0.3, 12);
    const wheelMaterial = new THREE.MeshLambertMaterial({ color: '#1A1A1A' });
    const rimGeometry = new THREE.CylinderGeometry(0.25, 0.25, 0.35, 8);
    const rimMaterial = new THREE.MeshLambertMaterial({ color: '#C0C0C0' });

    const wheelPositions = [
      { x: -1, z: 1.3 }, { x: 1, z: 1.3 },
      { x: -1, z: -1.3 }, { x: 1, z: -1.3 }
    ];

    wheelPositions.forEach(pos => {
      const wheelGroup = new THREE.Group();

      const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
      wheel.rotation.z = Math.PI / 2;
      wheel.castShadow = true;
      wheelGroup.add(wheel);

      const rim = new THREE.Mesh(rimGeometry, rimMaterial);
      rim.rotation.z = Math.PI / 2;
      rim.castShadow = true;
      wheelGroup.add(rim);

      wheelGroup.position.set(pos.x, 0.4, pos.z);
      carGroup.add(wheelGroup);
    });

    // Headlights
    const headlightGeometry = new THREE.SphereGeometry(0.15, 8, 8);
    const headlightMaterial = new THREE.MeshBasicMaterial({
      color: '#FFFF88'
    });

    [-0.6, 0.6].forEach(x => {
      const headlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
      headlight.position.set(x, 0.5, 2.1);
      carGroup.add(headlight);
    });

    // Taillights
    const taillightMaterial = new THREE.MeshBasicMaterial({
      color: '#FF4444'
    });

    [-0.5, 0.5].forEach(x => {
      const taillight = new THREE.Mesh(headlightGeometry, taillightMaterial);
      taillight.position.set(x, 0.5, -2.1);
      carGroup.add(taillight);
    });

    carGroup.position.set(20, 0.5, 0); // Position car on the track
    scene.add(carGroup);

    // Car movement variables
    let carSpeed = 0;
    let carRotation = 0;
    const maxSpeed = 0.3;
    const acceleration = 0.01;
    const deceleration = 0.95;
    const turnSpeed = 0.03;

    // Add trees around the track
    const createTree = (x: number, z: number) => {
      const treeGroup = new THREE.Group();

      // Tree trunk
      const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.5, 4);
      const trunkMaterial = new THREE.MeshLambertMaterial({ color: '#8B4513' });
      const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
      trunk.position.y = 2;
      trunk.castShadow = true;
      treeGroup.add(trunk);

      // Tree leaves
      const leavesGeometry = new THREE.SphereGeometry(2.5, 8, 8);
      const leavesMaterial = new THREE.MeshLambertMaterial({ color: '#228B22' });
      const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
      leaves.position.y = 5;
      leaves.castShadow = true;
      treeGroup.add(leaves);

      treeGroup.position.set(x, 0, z);
      scene.add(treeGroup);
    };

    // Place trees in a circle around the track
    for (let i = 0; i < 16; i++) {
      const angle = (i / 16) * Math.PI * 2;
      const radius = 35 + Math.random() * 10;
      createTree(
        Math.cos(angle) * radius,
        Math.sin(angle) * radius
      );
    }

    // Add buildings in the distance
    const createBuilding = (x: number, z: number, height: number) => {
      const buildingGeometry = new THREE.BoxGeometry(
        3 + Math.random() * 2,
        height,
        3 + Math.random() * 2
      );
      const buildingMaterial = new THREE.MeshLambertMaterial({
        color: new THREE.Color().setHSL(0.6, 0.3, 0.4 + Math.random() * 0.3)
      });
      const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
      building.position.set(x, height / 2, z);
      building.castShadow = true;
      scene.add(building);
    };

    // Place buildings
    for (let i = 0; i < 12; i++) {
      const angle = (i / 12) * Math.PI * 2;
      const radius = 60 + Math.random() * 20;
      createBuilding(
        Math.cos(angle) * radius,
        Math.sin(angle) * radius,
        8 + Math.random() * 12
      );
    }

    // Add clouds
    const createCloud = (x: number, y: number, z: number) => {
      const cloudGroup = new THREE.Group();
      const cloudMaterial = new THREE.MeshBasicMaterial({
        color: '#FFFFFF',
        transparent: true,
        opacity: 0.8
      });

      for (let i = 0; i < 4; i++) {
        const cloudGeometry = new THREE.SphereGeometry(2 + Math.random() * 2, 8, 8);
        const cloudPart = new THREE.Mesh(cloudGeometry, cloudMaterial);
        cloudPart.position.set(
          (Math.random() - 0.5) * 6,
          (Math.random() - 0.5) * 2,
          (Math.random() - 0.5) * 6
        );
        cloudGroup.add(cloudPart);
      }

      cloudGroup.position.set(x, y, z);
      scene.add(cloudGroup);
    };

    // Add clouds in the sky
    for (let i = 0; i < 8; i++) {
      createCloud(
        (Math.random() - 0.5) * 150,
        25 + Math.random() * 15,
        (Math.random() - 0.5) * 150
      );
    }

    // Add some decorative objects on the track
    const createCone = (x: number, z: number) => {
      const coneGeometry = new THREE.ConeGeometry(0.3, 1, 8);
      const coneMaterial = new THREE.MeshLambertMaterial({ color: '#FF4500' });
      const cone = new THREE.Mesh(coneGeometry, coneMaterial);
      cone.position.set(x, 0.5, z);
      cone.castShadow = true;
      scene.add(cone);
    };

    // Place traffic cones around the track
    for (let i = 0; i < 8; i++) {
      const angle = (i / 8) * Math.PI * 2;
      const radius = 28;
      createCone(
        Math.cos(angle) * radius,
        Math.sin(angle) * radius
      );
    }

    // Animation loop
    let animationId: number;
    const animate = () => {
      animationId = requestAnimationFrame(animate);

      // Handle car movement based on controls
      let targetSpeed = 0;

      // Debug: Log controls (remove this later)
      if (controls.forward || controls.backward || controls.left || controls.right) {
        console.log('Controls:', controls);
      }

      // Forward/Backward movement
      if (controls.forward) {
        targetSpeed = maxSpeed;
        if (controls.boost) {
          targetSpeed *= 1.5; // Boost multiplier
        }
      } else if (controls.backward) {
        targetSpeed = -maxSpeed * 0.6; // Reverse is slower
      }

      // Smooth speed interpolation
      if (targetSpeed !== 0) {
        carSpeed += (targetSpeed - carSpeed) * acceleration;
      } else {
        carSpeed *= deceleration; // Gradual deceleration
      }

      // Apply braking
      if (controls.brake) {
        carSpeed *= 0.85;
      }

      // Handle turning (only when moving)
      if (Math.abs(carSpeed) > 0.01) {
        if (controls.left) {
          carRotation += turnSpeed * Math.abs(carSpeed) / maxSpeed;
        }
        if (controls.right) {
          carRotation -= turnSpeed * Math.abs(carSpeed) / maxSpeed;
        }
      }

      // Apply rotation to car
      carGroup.rotation.y = carRotation;

      // Move car forward/backward based on its rotation
      if (Math.abs(carSpeed) > 0.001) {
        const forward = new THREE.Vector3(0, 0, -1);
        forward.applyQuaternion(carGroup.quaternion);
        forward.multiplyScalar(carSpeed);
        carGroup.position.add(forward);
      }

      // Keep car on ground level
      carGroup.position.y = 0.5;

      // Update speed display (convert to km/h scale)
      const displaySpeed = Math.abs(carSpeed) * 200; // Scale for realistic km/h display
      onSpeedUpdate(displaySpeed);

      // Update camera to follow car (chase camera)
      const cameraOffset = new THREE.Vector3(0, 8, 15);
      cameraOffset.applyQuaternion(carGroup.quaternion);
      const targetCameraPosition = carGroup.position.clone().add(cameraOffset);

      // Smooth camera movement
      camera.position.lerp(targetCameraPosition, 0.1);
      camera.lookAt(carGroup.position);

      renderer.render(scene, camera);
    };

    animate();

    // Handle resize
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      cancelAnimationFrame(animationId);
      renderer.dispose();
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 w-full h-full"
      tabIndex={0}
      style={{ outline: 'none' }}
    />
  );
};

function App() {
  const { gameState, startGame, resetGame, updateGameState } = useGameState();
  const [currentSpeed, setCurrentSpeed] = useState(0);

  const handleSpeedUpdate = (speed: number) => {
    setCurrentSpeed(speed);
    updateGameState({ speed });
  };

  return (
    <div className="relative w-full h-screen overflow-hidden bg-gradient-to-b from-blue-900 to-blue-600">
      {/* Simple start screen without complex components */}
      {!gameState.isPlaying ? (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-black/40 backdrop-blur-md rounded-2xl p-8 border border-white/20 text-center max-w-md mx-4">
            <h1 className="text-4xl font-bold text-white mb-2">
              3D Racing Game
            </h1>
            <p className="text-blue-300 mb-6">
              Experience high-speed racing in stunning 3D
            </p>

            <button
              onClick={startGame}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700
                       text-white font-bold py-4 px-8 rounded-xl transition-all duration-200
                       transform hover:scale-105 flex items-center space-x-2 mx-auto"
            >
              <span>START RACE</span>
            </button>

            <div className="mt-6 text-sm text-gray-300">
              <p><strong>Controls:</strong></p>
              <p>WASD / Arrow Keys - Drive</p>
              <p>Space - Brake</p>
              <p>Shift - Boost</p>
            </div>
          </div>
        </div>
      ) : (
        <>
          {/* 3D Scene */}
          <Simple3DScene onSpeedUpdate={handleSpeedUpdate} />

          {/* Game HUD */}
          <div className="absolute top-4 left-4 bg-black/20 backdrop-blur-md rounded-xl p-4 border border-white/10 text-white pointer-events-auto">
            <div className="text-lg font-bold">Speed: {Math.round(gameState.speed)} km/h</div>
            <div className="text-sm">Use WASD to control the car</div>
            <div className="text-xs mt-2 text-gray-300">
              W/↑ - Forward | S/↓ - Reverse<br />
              A/← - Left | D/→ - Right<br />
              Space - Brake | Shift - Boost
            </div>
            <div className="text-xs mt-2 text-yellow-300">
              Click on the 3D scene first, then use controls
            </div>
          </div>

          {/* Reset button */}
          <div className="absolute top-4 right-4">
            <button
              onClick={resetGame}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-xl transition-all pointer-events-auto"
            >
              Reset Game
            </button>
          </div>
        </>
      )}
    </div>
  );
}

export default App;