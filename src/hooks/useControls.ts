import { useState, useEffect, useCallback } from 'react';
import { Controls } from '../types/Game';

const initialControls: Controls = {
  forward: false,
  backward: false,
  left: false,
  right: false,
  brake: false,
  boost: false,
};

export const useControls = () => {
  const [controls, setControls] = useState<Controls>(initialControls);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Prevent default behavior for game keys
    if (['KeyW', 'KeyA', 'KeyS', 'KeyD', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Space', 'ShiftLeft', 'ShiftRight'].includes(event.code)) {
      event.preventDefault();
    }

    switch (event.code) {
      case 'KeyW':
      case 'ArrowUp':
        setControls(prev => ({ ...prev, forward: true }));
        break;
      case 'KeyS':
      case 'ArrowDown':
        setControls(prev => ({ ...prev, backward: true }));
        break;
      case 'KeyA':
      case 'ArrowLeft':
        setControls(prev => ({ ...prev, left: true }));
        break;
      case 'KeyD':
      case 'ArrowRight':
        setControls(prev => ({ ...prev, right: true }));
        break;
      case 'Space':
        setControls(prev => ({ ...prev, brake: true }));
        break;
      case 'ShiftLeft':
      case 'ShiftRight':
        setControls(prev => ({ ...prev, boost: true }));
        break;
    }
  }, []);

  const handleKeyUp = useCallback((event: KeyboardEvent) => {
    switch (event.code) {
      case 'KeyW':
      case 'ArrowUp':
        setControls(prev => ({ ...prev, forward: false }));
        break;
      case 'KeyS':
      case 'ArrowDown':
        setControls(prev => ({ ...prev, backward: false }));
        break;
      case 'KeyA':
      case 'ArrowLeft':
        setControls(prev => ({ ...prev, left: false }));
        break;
      case 'KeyD':
      case 'ArrowRight':
        setControls(prev => ({ ...prev, right: false }));
        break;
      case 'Space':
        setControls(prev => ({ ...prev, brake: false }));
        break;
      case 'ShiftLeft':
      case 'ShiftRight':
        setControls(prev => ({ ...prev, boost: false }));
        break;
    }
  }, []);

  useEffect(() => {
    // Add event listeners
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    // Cleanup
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleKeyDown, handleKeyUp]);

  return controls;
};