import { useState, useCallback } from 'react';
import { GameState } from '../types/Game';

const initialGameState: GameState = {
  isPlaying: false,
  isPaused: false,
  speed: 0,
  lapTime: 0,
  bestTime: 0,
  currentLap: 1,
  totalLaps: 3,
  position: { x: 0, y: 0, z: 0 },
  rotation: { x: 0, y: 0, z: 0 },
};

export const useGameState = () => {
  const [gameState, setGameState] = useState<GameState>(initialGameState);

  const startGame = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      isPlaying: true,
      isPaused: false,
      lapTime: 0,
    }));
  }, []);

  const pauseGame = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      isPaused: !prev.isPaused,
    }));
  }, []);

  const resetGame = useCallback(() => {
    setGameState(initialGameState);
  }, []);

  const updateGameState = useCallback((updates: Partial<GameState>) => {
    setGameState(prev => ({ ...prev, ...updates }));
  }, []);

  const completeLap = useCallback(() => {
    setGameState(prev => {
      const newBestTime = prev.bestTime === 0 || prev.lapTime < prev.bestTime 
        ? prev.lapTime 
        : prev.bestTime;
      
      return {
        ...prev,
        currentLap: prev.currentLap + 1,
        bestTime: newBestTime,
        lapTime: 0,
      };
    });
  }, []);

  return {
    gameState,
    startGame,
    pauseGame,
    resetGame,
    updateGameState,
    completeLap,
  };
};