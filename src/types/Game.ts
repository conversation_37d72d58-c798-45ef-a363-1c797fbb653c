export interface GameState {
  isPlaying: boolean;
  isPaused: boolean;
  speed: number;
  lapTime: number;
  bestTime: number;
  currentLap: number;
  totalLaps: number;
  position: { x: number; y: number; z: number };
  rotation: { x: number; y: number; z: number };
}

export interface Controls {
  forward: boolean;
  backward: boolean;
  left: boolean;
  right: boolean;
  brake: boolean;
  boost: boolean;
}

export interface CarPhysics {
  velocity: { x: number; y: number; z: number };
  acceleration: number;
  deceleration: number;
  maxSpeed: number;
  turnSpeed: number;
  boostMultiplier: number;
}

export type CameraMode = 'chase' | 'cockpit' | 'free';