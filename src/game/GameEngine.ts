import * as THREE from 'three';
import { CarController } from './CarController';
import { TrackGenerator } from './TrackGenerator';
import { Controls, GameState, CameraMode } from '../types/Game';

export class GameEngine {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private car: CarController;
  private track: TrackGenerator;
  private lights: THREE.Light[] = [];
  private clock: THREE.Clock;
  private cameraMode: CameraMode = 'chase';
  private cameraOffset: THREE.Vector3 = new THREE.Vector3(0, 5, 10);

  constructor(canvas: HTMLCanvasElement) {
    this.clock = new THREE.Clock();
    this.scene = new THREE.Scene();
    this.setupCamera();
    this.setupRenderer(canvas);
    this.setupLighting();
    this.car = new CarController();
    this.track = new TrackGenerator(this.scene);
    this.scene.add(this.car.mesh);
    this.setupPostProcessing();
  }

  private setupCamera(): void {
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 10, 15);
  }

  private setupRenderer(canvas: HTMLCanvasElement): void {
    this.renderer = new THREE.WebGLRenderer({ 
      canvas,
      antialias: true,
      alpha: true 
    });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.2;
  }

  private setupLighting(): void {
    // Ambient light
    const ambientLight = new THREE.AmbientLight('#87CEEB', 0.6);
    this.scene.add(ambientLight);
    this.lights.push(ambientLight);

    // Directional light (sun)
    const directionalLight = new THREE.DirectionalLight('#FFFFFF', 1);
    directionalLight.position.set(50, 50, 50);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 200;
    directionalLight.shadow.camera.left = -50;
    directionalLight.shadow.camera.right = 50;
    directionalLight.shadow.camera.top = 50;
    directionalLight.shadow.camera.bottom = -50;
    this.scene.add(directionalLight);
    this.lights.push(directionalLight);

    // Hemisphere light for realistic sky
    const hemisphereLight = new THREE.HemisphereLight('#87CEEB', '#228B22', 0.4);
    this.scene.add(hemisphereLight);
    this.lights.push(hemisphereLight);
  }

  private setupPostProcessing(): void {
    // Set up scene background
    this.scene.background = new THREE.Color('#87CEEB');
    this.scene.fog = new THREE.Fog('#87CEEB', 100, 200);
  }

  public update(controls: Controls, gameState: GameState): Partial<GameState> {
    if (!gameState.isPlaying || gameState.isPaused) {
      return {};
    }

    const deltaTime = this.clock.getDelta();
    
    // Update car with controls
    this.car.update(controls, deltaTime);
    
    // Update camera
    this.updateCamera();
    
    // Update game state
    const carPosition = this.car.getPosition();
    const carRotation = this.car.getRotation();
    const speed = this.car.getSpeed();
    
    return {
      speed,
      position: { x: carPosition.x, y: carPosition.y, z: carPosition.z },
      rotation: { x: carRotation.x, y: carRotation.y, z: carRotation.z },
      lapTime: gameState.lapTime + deltaTime,
    };
  }

  private updateCamera(): void {
    const carPosition = this.car.getPosition();
    const carRotation = this.car.mesh.rotation;

    switch (this.cameraMode) {
      case 'chase':
        // Chase camera behind the car
        const offset = new THREE.Vector3(0, 6, 12);
        offset.applyQuaternion(this.car.mesh.quaternion);
        
        const targetPosition = carPosition.clone().add(offset);
        this.camera.position.lerp(targetPosition, 0.1);
        this.camera.lookAt(carPosition);
        break;
        
      case 'cockpit':
        // First person view from inside the car
        const cockpitOffset = new THREE.Vector3(0, 1.2, 0.5);
        cockpitOffset.applyQuaternion(this.car.mesh.quaternion);
        this.camera.position.copy(carPosition).add(cockpitOffset);
        
        const lookAhead = new THREE.Vector3(0, 0, -10);
        lookAhead.applyQuaternion(this.car.mesh.quaternion);
        this.camera.lookAt(carPosition.clone().add(lookAhead));
        break;
        
      case 'free':
        // Free camera orbiting around the car
        const time = Date.now() * 0.0005;
        const radius = 25;
        this.camera.position.set(
          carPosition.x + Math.cos(time) * radius,
          carPosition.y + 12,
          carPosition.z + Math.sin(time) * radius
        );
        this.camera.lookAt(carPosition);
        break;
    }
  }

  public render(): void {
    this.renderer.render(this.scene, this.camera);
  }

  public resize(width: number, height: number): void {
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);
  }

  public setCameraMode(mode: CameraMode): void {
    this.cameraMode = mode;
  }

  public resetGame(): void {
    this.car.reset();
    this.clock.start();
  }

  public dispose(): void {
    this.renderer.dispose();
    this.scene.clear();
  }
}