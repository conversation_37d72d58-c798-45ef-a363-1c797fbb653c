import * as THREE from 'three';
import { Controls, CarPhysics } from '../types/Game';

export class CarController {
  public mesh: THREE.Group;
  public body: THREE.Box3;
  public physics: CarPhysics;
  private velocity: THREE.Vector3;
  private position: THREE.Vector3;
  private rotation: THREE.Euler;
  private currentSpeed: number = 0;

  constructor() {
    this.mesh = new THREE.Group();
    this.body = new THREE.Box3();
    this.velocity = new THREE.Vector3();
    this.position = new THREE.Vector3(0, 0.5, 0);
    this.rotation = new THREE.Euler();
    
    this.physics = {
      velocity: { x: 0, y: 0, z: 0 },
      acceleration: 0.05,
      deceleration: 0.95,
      maxSpeed: 3,
      turnSpeed: 0.03,
      boostMultiplier: 1.8,
    };

    this.createCar();
    this.mesh.position.copy(this.position);
  }

  private createCar(): void {
    // Car body
    const bodyGeometry = new THREE.BoxGeometry(0.8, 0.4, 2);
    const bodyMaterial = new THREE.MeshLambertMaterial({ 
      color: '#3B82F6',
      transparent: true,
      opacity: 0.9 
    });
    const carBody = new THREE.Mesh(bodyGeometry, bodyMaterial);
    carBody.position.y = 0.2;
    carBody.castShadow = true;
    this.mesh.add(carBody);

    // Car roof
    const roofGeometry = new THREE.BoxGeometry(0.6, 0.3, 1);
    const roofMaterial = new THREE.MeshLambertMaterial({ color: '#1E40AF' });
    const carRoof = new THREE.Mesh(roofGeometry, roofMaterial);
    carRoof.position.y = 0.6;
    carRoof.position.z = -0.2;
    carRoof.castShadow = true;
    this.mesh.add(carRoof);

    // Wheels
    const wheelGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.1, 8);
    const wheelMaterial = new THREE.MeshLambertMaterial({ color: '#1F2937' });
    
    const positions = [
      { x: -0.5, z: 0.7 },
      { x: 0.5, z: 0.7 },
      { x: -0.5, z: -0.7 },
      { x: 0.5, z: -0.7 },
    ];

    positions.forEach(pos => {
      const wheel = new THREE.Mesh(wheelGeometry, wheelMaterial);
      wheel.position.set(pos.x, 0, pos.z);
      wheel.rotation.z = Math.PI / 2;
      wheel.castShadow = true;
      this.mesh.add(wheel);
    });

    // Headlights
    const headlightGeometry = new THREE.SphereGeometry(0.08, 8, 8);
    const headlightMaterial = new THREE.MeshBasicMaterial({ 
      color: '#FFFFFF',
      emissive: '#FFFF88',
      emissiveIntensity: 0.5 
    });
    
    [-0.25, 0.25].forEach(x => {
      const headlight = new THREE.Mesh(headlightGeometry, headlightMaterial);
      headlight.position.set(x, 0.3, 1.1);
      this.mesh.add(headlight);
    });

    // Update bounding box
    this.body.setFromObject(this.mesh);
  }

  public update(controls: Controls, deltaTime: number): void {
    // Handle acceleration and deceleration
    let targetSpeed = 0;
    
    if (controls.forward) {
      targetSpeed = this.physics.maxSpeed;
      if (controls.boost) {
        targetSpeed *= this.physics.boostMultiplier;
      }
    } else if (controls.backward) {
      targetSpeed = -this.physics.maxSpeed * 0.6;
    }

    // Smooth speed interpolation
    if (targetSpeed !== 0) {
      this.currentSpeed += (targetSpeed - this.currentSpeed) * this.physics.acceleration;
    } else {
      this.currentSpeed *= this.physics.deceleration;
    }

    // Apply braking
    if (controls.brake) {
      this.currentSpeed *= 0.85;
    }

    // Handle turning (only when moving)
    if (Math.abs(this.currentSpeed) > 0.1) {
      let turnAmount = 0;
      if (controls.left) turnAmount += this.physics.turnSpeed;
      if (controls.right) turnAmount -= this.physics.turnSpeed;
      
      if (turnAmount !== 0) {
        // Scale turn speed with current speed
        turnAmount *= Math.abs(this.currentSpeed) / this.physics.maxSpeed;
        this.mesh.rotateY(turnAmount);
      }
    }

    // Move the car forward/backward based on its current rotation
    if (Math.abs(this.currentSpeed) > 0.01) {
      const forward = new THREE.Vector3(0, 0, -1);
      forward.applyQuaternion(this.mesh.quaternion);
      forward.multiplyScalar(this.currentSpeed * deltaTime * 60); // Scale with deltaTime
      
      this.mesh.position.add(forward);
    }

    // Keep car on ground
    this.mesh.position.y = 0.5;

    // Update velocity for physics calculations
    this.velocity.set(0, 0, -this.currentSpeed);
    this.velocity.applyQuaternion(this.mesh.quaternion);

    // Update physics data
    this.physics.velocity = {
      x: this.velocity.x,
      y: this.velocity.y,
      z: this.velocity.z,
    };

    // Update bounding box
    this.body.setFromObject(this.mesh);
  }

  public getSpeed(): number {
    return Math.abs(this.currentSpeed) * 50; // Convert to km/h scale
  }

  public getPosition(): THREE.Vector3 {
    return this.mesh.position.clone();
  }

  public getRotation(): THREE.Euler {
    return this.mesh.rotation.clone();
  }

  public reset(): void {
    this.mesh.position.set(0, 0.5, 0);
    this.mesh.rotation.set(0, 0, 0);
    this.velocity.set(0, 0, 0);
    this.currentSpeed = 0;
  }
}