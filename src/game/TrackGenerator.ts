import * as THREE from 'three';

export class TrackGenerator {
  public track: THREE.Group;
  public checkpoints: THREE.Vector3[];
  private scene: THREE.Scene;

  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.track = new THREE.Group();
    this.checkpoints = [];
    this.generateTrack();
    scene.add(this.track);
  }

  private generateTrack(): void {
    // Create ground plane
    const groundGeometry = new THREE.PlaneGeometry(200, 200);
    const groundMaterial = new THREE.MeshLambertMaterial({ 
      color: '#059669',
      transparent: true,
      opacity: 0.8 
    });
    const ground = new THREE.Mesh(groundGeometry, groundMaterial);
    ground.rotation.x = -Math.PI / 2;
    ground.receiveShadow = true;
    this.track.add(ground);

    // Create track surface
    this.createRaceTrack();
    
    // Add environment elements
    this.addEnvironmentElements();
    
    // Generate checkpoints
    this.generateCheckpoints();
  }

  private createRaceTrack(): void {
    const trackPoints = this.getTrackPath();
    
    // Create track surface
    const trackGeometry = new THREE.BufferGeometry();
    const vertices: number[] = [];
    const indices: number[] = [];
    const trackWidth = 8;

    for (let i = 0; i < trackPoints.length; i++) {
      const point = trackPoints[i];
      const nextPoint = trackPoints[(i + 1) % trackPoints.length];
      
      // Calculate perpendicular vector for track width
      const direction = new THREE.Vector3()
        .subVectors(nextPoint, point)
        .normalize();
      const perpendicular = new THREE.Vector3(-direction.z, 0, direction.x)
        .multiplyScalar(trackWidth / 2);

      // Create track vertices
      const leftPoint = point.clone().add(perpendicular);
      const rightPoint = point.clone().sub(perpendicular);
      
      vertices.push(
        leftPoint.x, leftPoint.y + 0.1, leftPoint.z,
        rightPoint.x, rightPoint.y + 0.1, rightPoint.z
      );

      // Create track indices
      if (i < trackPoints.length - 1) {
        const vertexIndex = i * 2;
        indices.push(
          vertexIndex, vertexIndex + 1, vertexIndex + 2,
          vertexIndex + 1, vertexIndex + 3, vertexIndex + 2
        );
      }
    }

    trackGeometry.setIndex(indices);
    trackGeometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
    trackGeometry.computeVertexNormals();

    const trackMaterial = new THREE.MeshLambertMaterial({ 
      color: '#374151',
      side: THREE.DoubleSide 
    });
    const trackMesh = new THREE.Mesh(trackGeometry, trackMaterial);
    trackMesh.receiveShadow = true;
    this.track.add(trackMesh);

    // Add track barriers
    this.addTrackBarriers(trackPoints, trackWidth);
  }

  private getTrackPath(): THREE.Vector3[] {
    const points: THREE.Vector3[] = [];
    const radius = 30;
    const segments = 32;

    for (let i = 0; i < segments; i++) {
      const angle = (i / segments) * Math.PI * 2;
      // Create an oval track with some variation
      const x = Math.cos(angle) * (radius + Math.sin(angle * 3) * 5);
      const z = Math.sin(angle) * (radius * 0.7 + Math.cos(angle * 2) * 3);
      points.push(new THREE.Vector3(x, 0, z));
    }

    return points;
  }

  private addTrackBarriers(trackPoints: THREE.Vector3[], trackWidth: number): void {
    const barrierHeight = 1;
    const barrierGeometry = new THREE.BoxGeometry(1, barrierHeight, 1);
    const barrierMaterial = new THREE.MeshLambertMaterial({ color: '#DC2626' });

    for (let i = 0; i < trackPoints.length; i += 3) {
      const point = trackPoints[i];
      const nextPoint = trackPoints[(i + 1) % trackPoints.length];
      
      const direction = new THREE.Vector3()
        .subVectors(nextPoint, point)
        .normalize();
      const perpendicular = new THREE.Vector3(-direction.z, 0, direction.x)
        .multiplyScalar((trackWidth / 2) + 1);

      // Left barrier
      const leftBarrier = new THREE.Mesh(barrierGeometry, barrierMaterial);
      leftBarrier.position.copy(point.clone().add(perpendicular));
      leftBarrier.position.y = barrierHeight / 2;
      leftBarrier.castShadow = true;
      this.track.add(leftBarrier);

      // Right barrier
      const rightBarrier = new THREE.Mesh(barrierGeometry, barrierMaterial);
      rightBarrier.position.copy(point.clone().sub(perpendicular));
      rightBarrier.position.y = barrierHeight / 2;
      rightBarrier.castShadow = true;
      this.track.add(rightBarrier);
    }
  }

  private addEnvironmentElements(): void {
    // Add trees around the track
    for (let i = 0; i < 50; i++) {
      const tree = this.createTree();
      const angle = Math.random() * Math.PI * 2;
      const distance = 50 + Math.random() * 30;
      tree.position.set(
        Math.cos(angle) * distance,
        0,
        Math.sin(angle) * distance
      );
      this.track.add(tree);
    }

    // Add clouds
    for (let i = 0; i < 20; i++) {
      const cloud = this.createCloud();
      cloud.position.set(
        (Math.random() - 0.5) * 200,
        20 + Math.random() * 20,
        (Math.random() - 0.5) * 200
      );
      this.track.add(cloud);
    }
  }

  private createTree(): THREE.Group {
    const tree = new THREE.Group();
    
    // Trunk
    const trunkGeometry = new THREE.CylinderGeometry(0.3, 0.5, 3);
    const trunkMaterial = new THREE.MeshLambertMaterial({ color: '#92400E' });
    const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
    trunk.position.y = 1.5;
    trunk.castShadow = true;
    tree.add(trunk);

    // Leaves
    const leavesGeometry = new THREE.SphereGeometry(2, 8, 8);
    const leavesMaterial = new THREE.MeshLambertMaterial({ color: '#16A34A' });
    const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
    leaves.position.y = 4;
    leaves.castShadow = true;
    tree.add(leaves);

    return tree;
  }

  private createCloud(): THREE.Group {
    const cloud = new THREE.Group();
    const cloudMaterial = new THREE.MeshBasicMaterial({ 
      color: '#FFFFFF',
      transparent: true,
      opacity: 0.7 
    });

    for (let i = 0; i < 3 + Math.random() * 3; i++) {
      const cloudGeometry = new THREE.SphereGeometry(
        1 + Math.random() * 2,
        8,
        8
      );
      const cloudPart = new THREE.Mesh(cloudGeometry, cloudMaterial);
      cloudPart.position.set(
        (Math.random() - 0.5) * 4,
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 4
      );
      cloud.add(cloudPart);
    }

    return cloud;
  }

  private generateCheckpoints(): void {
    const trackPoints = this.getTrackPath();
    const checkpointInterval = Math.floor(trackPoints.length / 8);
    
    for (let i = 0; i < trackPoints.length; i += checkpointInterval) {
      this.checkpoints.push(trackPoints[i].clone());
    }
  }

  public getStartPosition(): THREE.Vector3 {
    return this.checkpoints[0] ? this.checkpoints[0].clone() : new THREE.Vector3(0, 0.5, 0);
  }

  public checkLapCompletion(carPosition: THREE.Vector3): boolean {
    // Simple lap completion check - car must pass through all checkpoints in order
    const distanceThreshold = 5;
    return this.checkpoints.every(checkpoint => 
      carPosition.distanceTo(checkpoint) < distanceThreshold
    );
  }
}