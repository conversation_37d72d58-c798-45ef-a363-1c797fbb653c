import React, { useRef, useEffect } from 'react';
import { GameEngine } from '../game/GameEngine';
import { Controls, GameState, CameraMode } from '../types/Game';

interface GameCanvasProps {
  controls: Controls;
  gameState: GameState;
  onGameStateUpdate: (updates: Partial<GameState>) => void;
  cameraMode: CameraMode;
}

export const GameCanvas: React.FC<GameCanvasProps> = ({
  controls,
  gameState,
  onGameStateUpdate,
  cameraMode,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const gameEngineRef = useRef<GameEngine | null>(null);
  const animationFrameRef = useRef<number>();

  useEffect(() => {
    if (!canvasRef.current) return;

    // Initialize game engine
    gameEngineRef.current = new GameEngine(canvasRef.current);

    // Handle window resize
    const handleResize = () => {
      if (gameEngineRef.current) {
        gameEngineRef.current.resize(window.innerWidth, window.innerHeight);
      }
    };

    window.addEventListener('resize', handleResize);

    // Game loop
    const gameLoop = () => {
      if (gameEngineRef.current) {
        const updates = gameEngineRef.current.update(controls, gameState);
        if (Object.keys(updates).length > 0) {
          onGameStateUpdate(updates);
        }
        gameEngineRef.current.render();
      }
      animationFrameRef.current = requestAnimationFrame(gameLoop);
    };

    gameLoop();

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (gameEngineRef.current) {
        gameEngineRef.current.dispose();
      }
    };
  }, []);

  // Update camera mode
  useEffect(() => {
    if (gameEngineRef.current) {
      gameEngineRef.current.setCameraMode(cameraMode);
    }
  }, [cameraMode]);

  // Reset game when needed
  useEffect(() => {
    if (!gameState.isPlaying && gameEngineRef.current) {
      gameEngineRef.current.resetGame();
    }
  }, [gameState.isPlaying]);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 w-full h-full"
      style={{ touchAction: 'none' }}
    />
  );
};