import React from 'react';
import { GameState, CameraMode } from '../types/Game';
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Camera, 
  Gauge,
  Timer,
  Trophy,
  MapPin
} from 'lucide-react';

interface GameHUDProps {
  gameState: GameState;
  onStartGame: () => void;
  onPauseGame: () => void;
  onResetGame: () => void;
  onCameraChange: (mode: CameraMode) => void;
  currentCamera: CameraMode;
}

export const GameHUD: React.FC<GameHUDProps> = ({
  gameState,
  onStartGame,
  onPauseGame,
  onResetGame,
  onCameraChange,
  currentCamera,
}) => {
  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = (time % 60).toFixed(1);
    return `${minutes}:${seconds.padStart(4, '0')}`;
  };

  return (
    <div className="absolute inset-0 pointer-events-none z-10">
      {/* Top HUD */}
      <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
        {/* Speed and Performance */}
        <div className="bg-black/20 backdrop-blur-md rounded-xl p-4 border border-white/10 pointer-events-auto">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Gauge className="w-5 h-5 text-blue-400" />
              <div>
                <div className="text-2xl font-bold text-white">
                  {Math.round(gameState.speed)}
                </div>
                <div className="text-xs text-blue-300">KM/H</div>
              </div>
            </div>
            
            <div className="w-px h-8 bg-white/20" />
            
            <div className="flex items-center space-x-2">
              <Timer className="w-5 h-5 text-green-400" />
              <div>
                <div className="text-lg font-bold text-white">
                  {formatTime(gameState.lapTime)}
                </div>
                <div className="text-xs text-green-300">LAP TIME</div>
              </div>
            </div>
            
            {gameState.bestTime > 0 && (
              <>
                <div className="w-px h-8 bg-white/20" />
                <div className="flex items-center space-x-2">
                  <Trophy className="w-5 h-5 text-yellow-400" />
                  <div>
                    <div className="text-lg font-bold text-white">
                      {formatTime(gameState.bestTime)}
                    </div>
                    <div className="text-xs text-yellow-300">BEST</div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Race Progress */}
        <div className="bg-black/20 backdrop-blur-md rounded-xl p-4 border border-white/10">
          <div className="flex items-center space-x-2">
            <MapPin className="w-5 h-5 text-orange-400" />
            <div>
              <div className="text-lg font-bold text-white">
                {gameState.currentLap}/{gameState.totalLaps}
              </div>
              <div className="text-xs text-orange-300">LAPS</div>
            </div>
          </div>
        </div>
      </div>

      {/* Game Controls */}
      <div className="absolute top-4 right-4">
        <div className="flex space-x-2">
          <button
            onClick={() => onCameraChange(
              currentCamera === 'chase' ? 'cockpit' : 
              currentCamera === 'cockpit' ? 'free' : 'chase'
            )}
            className="bg-black/20 backdrop-blur-md rounded-xl p-3 border border-white/10 
                     hover:bg-white/10 transition-all duration-200 pointer-events-auto
                     text-white hover:scale-105"
            title="Switch Camera"
          >
            <Camera className="w-5 h-5" />
          </button>
          
          <button
            onClick={onResetGame}
            className="bg-black/20 backdrop-blur-md rounded-xl p-3 border border-white/10 
                     hover:bg-white/10 transition-all duration-200 pointer-events-auto
                     text-white hover:scale-105"
            title="Reset Game"
          >
            <RotateCcw className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Start Screen */}
      {!gameState.isPlaying && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center">
          <div className="bg-black/40 backdrop-blur-md rounded-2xl p-8 border border-white/20 text-center max-w-md mx-4">
            <h1 className="text-4xl font-bold text-white mb-2">
              3D Racing
            </h1>
            <p className="text-blue-300 mb-6">
              Experience high-speed racing in stunning 3D
            </p>
            
            <button
              onClick={onStartGame}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700
                       text-white font-bold py-4 px-8 rounded-xl transition-all duration-200
                       transform hover:scale-105 flex items-center space-x-2 mx-auto pointer-events-auto"
            >
              <Play className="w-6 h-6" />
              <span>START RACE</span>
            </button>
            
            <div className="mt-6 text-sm text-gray-300">
              <p><strong>Controls:</strong></p>
              <p>WASD / Arrow Keys - Drive</p>
              <p>Space - Brake</p>
              <p>Shift - Boost</p>
            </div>
          </div>
        </div>
      )}

      {/* Pause Screen */}
      {gameState.isPlaying && gameState.isPaused && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center">
          <div className="bg-black/40 backdrop-blur-md rounded-2xl p-8 border border-white/20 text-center">
            <h2 className="text-3xl font-bold text-white mb-6">PAUSED</h2>
            <button
              onClick={onPauseGame}
              className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700
                       text-white font-bold py-3 px-6 rounded-xl transition-all duration-200
                       transform hover:scale-105 flex items-center space-x-2 mx-auto pointer-events-auto"
            >
              <Play className="w-5 h-5" />
              <span>RESUME</span>
            </button>
          </div>
        </div>
      )}

      {/* Pause Button */}
      {gameState.isPlaying && !gameState.isPaused && (
        <div className="absolute bottom-4 right-4">
          <button
            onClick={onPauseGame}
            className="bg-black/20 backdrop-blur-md rounded-xl p-4 border border-white/10 
                     hover:bg-white/10 transition-all duration-200 pointer-events-auto
                     text-white hover:scale-105"
          >
            <Pause className="w-6 h-6" />
          </button>
        </div>
      )}

      {/* Speed Indicator */}
      {gameState.isPlaying && (
        <div className="absolute bottom-4 left-4">
          <div className="bg-black/20 backdrop-blur-md rounded-full p-4 border border-white/10">
            <div className="relative w-24 h-24">
              <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="rgb(255 255 255 / 0.2)"
                  strokeWidth="6"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  fill="none"
                  stroke="rgb(59 130 246)"
                  strokeWidth="6"
                  strokeLinecap="round"
                  strokeDasharray="283"
                  strokeDashoffset={283 - (gameState.speed / 200) * 283}
                  className="transition-all duration-200"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-lg font-bold text-white">
                    {Math.round(gameState.speed)}
                  </div>
                  <div className="text-xs text-blue-300">KM/H</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};